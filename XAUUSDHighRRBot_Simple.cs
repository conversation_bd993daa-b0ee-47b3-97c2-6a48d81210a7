using System;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class XAUUSDHighRRBotSimple : Robot
    {
        #region Parameters
        
        [Parameter("Risk Per Trade (%)", DefaultValue = 2.0, MinValue = 0.5, MaxValue = 5.0)]
        public double RiskPerTrade { get; set; }
        
        [Parameter("Min Risk Reward Ratio", DefaultValue = 2.5, MinValue = 2.0, MaxValue = 5.0)]
        public double MinRiskRewardRatio { get; set; }
        
        [Parameter("Max Trades Per Day", DefaultValue = 5, MinValue = 1, MaxValue = 10)]
        public int MaxTradesPerDay { get; set; }
        
        [Parameter("RSI Period", DefaultValue = 14, MinValue = 5, MaxValue = 30)]
        public int RSIPeriod { get; set; }
        
        [Parameter("MACD Fast EMA", DefaultValue = 12, MinValue = 5, MaxValue = 20)]
        public int MACDFastEMA { get; set; }
        
        [Parameter("MACD Slow EMA", DefaultValue = 26, MinValue = 20, MaxValue = 40)]
        public int MACDSlowEMA { get; set; }
        
        [Parameter("MACD Signal", DefaultValue = 9, MinValue = 5, MaxValue = 15)]
        public int MACDSignal { get; set; }
        
        [Parameter("ATR Period", DefaultValue = 14, MinValue = 10, MaxValue = 20)]
        public int ATRPeriod { get; set; }
        
        [Parameter("ATR Stop Loss Multiplier", DefaultValue = 1.5, MinValue = 1.0, MaxValue = 3.0)]
        public double ATRStopLossMultiplier { get; set; }
        
        [Parameter("ATR Take Profit Multiplier", DefaultValue = 3.75, MinValue = 2.5, MaxValue = 6.0)]
        public double ATRTakeProfitMultiplier { get; set; }
        
        [Parameter("Trading Start Hour (UTC)", DefaultValue = 8, MinValue = 0, MaxValue = 23)]
        public int TradingStartHour { get; set; }
        
        [Parameter("Trading End Hour (UTC)", DefaultValue = 17, MinValue = 0, MaxValue = 23)]
        public int TradingEndHour { get; set; }
        
        #endregion
        
        #region Indicators
        
        private RelativeStrengthIndex _rsi;
        private MacdCrossOver _macd;
        private BollingerBands _bollingerBands;
        private AverageTrueRange _atr;
        private SimpleMovingAverage _sma20;
        private SimpleMovingAverage _sma50;
        
        #endregion
        
        #region Private Variables
        
        private int _tradesCountToday;
        private DateTime _lastTradeDate;
        private double _dailyPnL;
        private bool _tradingEnabled;
        private double _currentSupport;
        private double _currentResistance;
        
        #endregion
        
        protected override void OnStart()
        {
            // Initialize indicators
            _rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, RSIPeriod);
            _macd = Indicators.MacdCrossOver(Bars.ClosePrices, MACDFastEMA, MACDSlowEMA, MACDSignal);
            _bollingerBands = Indicators.BollingerBands(Bars.ClosePrices, 20, 2.0, MovingAverageType.Simple);
            _atr = Indicators.AverageTrueRange(ATRPeriod);
            _sma20 = Indicators.SimpleMovingAverage(Bars.ClosePrices, 20);
            _sma50 = Indicators.SimpleMovingAverage(Bars.ClosePrices, 50);
            
            // Initialize variables
            _tradesCountToday = 0;
            _lastTradeDate = DateTime.MinValue;
            _dailyPnL = 0;
            _tradingEnabled = true;
            
            Print("XAUUSD High Risk-Reward Bot Started (Simple Version)");
            Print($"Risk per trade: {RiskPerTrade}%");
            Print($"Min Risk-Reward Ratio: {MinRiskRewardRatio}:1");
        }
        
        protected override void OnTick()
        {
            if (Bars.Count < 50) // Need enough data for indicators
                return;
                
            // Reset daily counters if new day
            CheckNewTradingDay();
            
            // Update support and resistance
            UpdateSupportResistance();
            
            // Check trading conditions
            if (!IsTradingAllowed())
                return;
                
            // Look for trading signals
            CheckForTradingSignals();
        }
        
        private void CheckNewTradingDay()
        {
            var currentDate = Server.Time.Date;
            
            if (_lastTradeDate != currentDate)
            {
                _tradesCountToday = 0;
                _dailyPnL = 0;
                _tradingEnabled = true;
                _lastTradeDate = currentDate;
                
                Print($"New trading day: {currentDate:yyyy-MM-dd}");
            }
        }
        
        private void UpdateSupportResistance()
        {
            var lookbackPeriod = 20;
            
            if (Bars.Count < lookbackPeriod)
                return;
            
            double maxHigh = double.MinValue;
            double minLow = double.MaxValue;
            
            for (int i = 0; i < lookbackPeriod; i++)
            {
                var index = Bars.Count - 1 - i;
                if (index >= 0)
                {
                    maxHigh = Math.Max(maxHigh, Bars.HighPrices[index]);
                    minLow = Math.Min(minLow, Bars.LowPrices[index]);
                }
            }
            
            _currentResistance = maxHigh;
            _currentSupport = minLow;
        }
        
        private bool IsTradingAllowed()
        {
            if (!_tradingEnabled)
                return false;
                
            if (_tradesCountToday >= MaxTradesPerDay)
                return false;
                
            var currentHour = Server.Time.Hour;
            if (currentHour < TradingStartHour || currentHour >= TradingEndHour)
                return false;
                
            if (Positions.Count > 0)
                return false;
                
            return true;
        }
        
        private void CheckForTradingSignals()
        {
            if (IsBullishSignal())
            {
                ExecuteBuyOrder();
            }
            else if (IsBearishSignal())
            {
                ExecuteSellOrder();
            }
        }
        
        private bool IsBullishSignal()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var rsiValue = _rsi.Result.LastValue;
            var macdMain = _macd.MACD.LastValue;
            var macdSignal = _macd.Signal.LastValue;
            var bbUpper = _bollingerBands.Top.LastValue;
            
            // Multi-confirmation bullish signal
            bool priceBreakout = currentPrice > _currentResistance;
            bool rsiConfirm = rsiValue > 50 && rsiValue < 80;
            bool macdConfirm = macdMain > macdSignal && macdMain > 0;
            bool bbConfirm = currentPrice > bbUpper;
            bool trendConfirm = _sma20.Result.LastValue > _sma50.Result.LastValue;
            
            return priceBreakout && rsiConfirm && macdConfirm && bbConfirm && trendConfirm;
        }
        
        private bool IsBearishSignal()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var rsiValue = _rsi.Result.LastValue;
            var macdMain = _macd.MACD.LastValue;
            var macdSignal = _macd.Signal.LastValue;
            var bbLower = _bollingerBands.Bottom.LastValue;
            
            // Multi-confirmation bearish signal
            bool priceBreakout = currentPrice < _currentSupport;
            bool rsiConfirm = rsiValue < 50 && rsiValue > 20;
            bool macdConfirm = macdMain < macdSignal && macdMain < 0;
            bool bbConfirm = currentPrice < bbLower;
            bool trendConfirm = _sma20.Result.LastValue < _sma50.Result.LastValue;
            
            return priceBreakout && rsiConfirm && macdConfirm && bbConfirm && trendConfirm;
        }
        
        private void ExecuteBuyOrder()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var atrValue = _atr.Result.LastValue;
            
            var stopLossDistance = atrValue * ATRStopLossMultiplier;
            var takeProfitDistance = atrValue * ATRTakeProfitMultiplier;
            
            // Ensure minimum risk-reward ratio
            var actualRiskReward = takeProfitDistance / stopLossDistance;
            if (actualRiskReward < MinRiskRewardRatio)
            {
                takeProfitDistance = stopLossDistance * MinRiskRewardRatio;
            }
            
            var stopLossPrice = currentPrice - stopLossDistance;
            var takeProfitPrice = currentPrice + takeProfitDistance;
            
            var riskAmount = Account.Balance * (RiskPerTrade / 100);
            var positionSize = CalculatePositionSize(riskAmount, stopLossDistance);
            
            if (positionSize > 0)
            {
                var result = ExecuteMarketOrder(TradeType.Buy, SymbolName, positionSize, "XAUUSD_Buy", 
                    stopLossPrice, takeProfitPrice);
                
                if (result.IsSuccessful)
                {
                    _tradesCountToday++;
                    Print($"Buy order executed: Size={positionSize}, SL={stopLossPrice:F2}, TP={takeProfitPrice:F2}");
                    Print($"Risk-Reward Ratio: {actualRiskReward:F2}:1");
                }
            }
        }
        
        private void ExecuteSellOrder()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var atrValue = _atr.Result.LastValue;
            
            var stopLossDistance = atrValue * ATRStopLossMultiplier;
            var takeProfitDistance = atrValue * ATRTakeProfitMultiplier;
            
            // Ensure minimum risk-reward ratio
            var actualRiskReward = takeProfitDistance / stopLossDistance;
            if (actualRiskReward < MinRiskRewardRatio)
            {
                takeProfitDistance = stopLossDistance * MinRiskRewardRatio;
            }
            
            var stopLossPrice = currentPrice + stopLossDistance;
            var takeProfitPrice = currentPrice - takeProfitDistance;
            
            var riskAmount = Account.Balance * (RiskPerTrade / 100);
            var positionSize = CalculatePositionSize(riskAmount, stopLossDistance);
            
            if (positionSize > 0)
            {
                var result = ExecuteMarketOrder(TradeType.Sell, SymbolName, positionSize, "XAUUSD_Sell", 
                    stopLossPrice, takeProfitPrice);
                
                if (result.IsSuccessful)
                {
                    _tradesCountToday++;
                    Print($"Sell order executed: Size={positionSize}, SL={stopLossPrice:F2}, TP={takeProfitPrice:F2}");
                    Print($"Risk-Reward Ratio: {actualRiskReward:F2}:1");
                }
            }
        }
        
        private long CalculatePositionSize(double riskAmount, double stopLossDistance)
        {
            var pipValue = Symbol.PipValue;
            var stopLossInPips = stopLossDistance / Symbol.PipSize;
            var positionSize = riskAmount / (stopLossInPips * pipValue);
            var volumeInUnits = Symbol.NormalizeVolumeInUnits(positionSize, RoundingMode.Down);
            
            if (volumeInUnits < Symbol.VolumeInUnitsMin)
                volumeInUnits = Symbol.VolumeInUnitsMin;
            
            return volumeInUnits;
        }
        
        protected override void OnPositionClosed(PositionClosedEventArgs args)
        {
            var position = args.Position;
            var pnl = position.NetProfit;
            
            _dailyPnL += pnl;
            
            Print($"Position closed: {position.TradeType} PnL: {pnl:F2} {Account.Asset.Name}");
            Print($"Daily PnL: {_dailyPnL:F2} {Account.Asset.Name}");
        }
        
        protected override void OnStop()
        {
            Print($"Bot stopped. Daily PnL: {_dailyPnL:F2} {Account.Asset.Name}");
        }
    }
}
