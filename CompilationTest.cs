using System;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class CompilationTest : Robot
    {
        protected override void OnStart()
        {
            Print("✅ Compilation Test - All methods and indicators working correctly!");
            
            // Test all indicator initializations
            var atr = Indicators.AverageTrueRange(Bars, 14, MovingAverageType.Simple);
            var rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, 14);
            var macd = Indicators.MacdCrossOver(Bars.ClosePrices, 12, 26, 9);
            var bb = Indicators.BollingerBands(Bars.ClosePrices, 20, 2.0, MovingAverageType.Simple);
            var ema = Indicators.ExponentialMovingAverage(Bars.ClosePrices, 20);
            var sma = Indicators.SimpleMovingAverage(Bars.ClosePrices, 50);
            
            Print("✅ All indicators initialized successfully");
            
            // Test position methods (without actually opening positions)
            TestPositionMethods();
            
            Print("✅ All position methods accessible");
            Print("✅ Compilation test completed successfully!");
        }
        
        private void TestPositionMethods()
        {
            // Test that all position-related methods are accessible
            var positions = Positions;
            var pendingOrders = PendingOrders;
            var history = History;
            
            // Test that we can access Symbol properties
            var bid = Symbol.Bid;
            var ask = Symbol.Ask;
            var pipSize = Symbol.PipSize;
            var pipValue = Symbol.PipValue;
            var minVolume = Symbol.VolumeInUnitsMin;
            var maxVolume = Symbol.VolumeInUnitsMax;
            
            Print($"Symbol info - Bid: {bid}, Ask: {ask}, PipSize: {pipSize}");
        }
        
        protected override void OnPositionOpened(Position position)
        {
            Print($"Position opened: {position.Id}");
        }
        
        protected override void OnPositionClosed(Position position)
        {
            Print($"Position closed: {position.Id}, PnL: {position.NetProfit}");
        }
        
        protected override void OnTick()
        {
            // Test basic functionality every 1000 ticks
            if (Bars.Count % 1000 == 0)
            {
                Print($"Tick test - Time: {Server.Time}, Price: {Symbol.Bid}");
            }
        }
        
        protected override void OnStop()
        {
            Print("✅ Compilation Test - Stopped successfully");
        }
    }
}
