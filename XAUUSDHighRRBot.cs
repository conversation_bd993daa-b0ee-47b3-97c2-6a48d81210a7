using System;
using System.Linq;
using System.Collections.Generic;
using cAlgo.API;
using cAlgo.API.Indicators;
using cAlgo.Indicators;

namespace cAlgo.Robots
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class XAUUSDHighRRBot : Robot
    {
        #region Parameters
        
        [Parameter("Risk Per Trade (%)", DefaultValue = 2.0, MinValue = 0.5, MaxValue = 5.0)]
        public double RiskPerTrade { get; set; }
        
        [Parameter("Min Risk Reward Ratio", DefaultValue = 2.5, MinValue = 2.0, MaxValue = 5.0)]
        public double MinRiskRewardRatio { get; set; }
        
        [Parameter("Max Trades Per Day", DefaultValue = 5, MinValue = 1, MaxValue = 10)]
        public int MaxTradesPerDay { get; set; }
        
        [Parameter("Max Consecutive Losses", DefaultValue = 3, MinValue = 1, MaxValue = 5)]
        public int MaxConsecutiveLosses { get; set; }
        
        [Parameter("RSI Period", DefaultValue = 14, MinValue = 5, MaxValue = 30)]
        public int RSIPeriod { get; set; }
        
        [Parameter("MACD Fast EMA", DefaultValue = 12, MinValue = 5, MaxValue = 20)]
        public int MACDFastEMA { get; set; }
        
        [Parameter("MACD Slow EMA", DefaultValue = 26, MinValue = 20, MaxValue = 40)]
        public int MACDSlowEMA { get; set; }
        
        [Parameter("MACD Signal", DefaultValue = 9, MinValue = 5, MaxValue = 15)]
        public int MACDSignal { get; set; }
        
        [Parameter("Bollinger Bands Period", DefaultValue = 20, MinValue = 10, MaxValue = 30)]
        public int BBPeriod { get; set; }
        
        [Parameter("Bollinger Bands Std Dev", DefaultValue = 2.0, MinValue = 1.5, MaxValue = 3.0)]
        public double BBStdDev { get; set; }
        
        [Parameter("ATR Period", DefaultValue = 14, MinValue = 10, MaxValue = 20)]
        public int ATRPeriod { get; set; }
        
        [Parameter("ATR Stop Loss Multiplier", DefaultValue = 1.5, MinValue = 1.0, MaxValue = 3.0)]
        public double ATRStopLossMultiplier { get; set; }
        
        [Parameter("ATR Take Profit Multiplier", DefaultValue = 3.75, MinValue = 2.5, MaxValue = 6.0)]
        public double ATRTakeProfitMultiplier { get; set; }
        
        [Parameter("Volume Confirmation", DefaultValue = true)]
        public bool UseVolumeConfirmation { get; set; }
        
        [Parameter("News Filter", DefaultValue = true)]
        public bool UseNewsFilter { get; set; }
        
        [Parameter("Trading Start Hour (UTC)", DefaultValue = 8, MinValue = 0, MaxValue = 23)]
        public int TradingStartHour { get; set; }
        
        [Parameter("Trading End Hour (UTC)", DefaultValue = 17, MinValue = 0, MaxValue = 23)]
        public int TradingEndHour { get; set; }

        [Parameter("Avoid Asian Session", DefaultValue = true)]
        public bool AvoidAsianSession { get; set; }

        [Parameter("Prefer London-NY Overlap", DefaultValue = true)]
        public bool PreferLondonNYOverlap { get; set; }

        [Parameter("Max Daily Drawdown (%)", DefaultValue = 5.0, MinValue = 2.0, MaxValue = 10.0)]
        public double MaxDailyDrawdown { get; set; }

        [Parameter("Trailing Stop", DefaultValue = true)]
        public bool UseTrailingStop { get; set; }

        [Parameter("Trailing Stop Distance (ATR)", DefaultValue = 1.0, MinValue = 0.5, MaxValue = 2.0)]
        public double TrailingStopDistance { get; set; }

        [Parameter("Break Even After (ATR)", DefaultValue = 1.5, MinValue = 1.0, MaxValue = 3.0)]
        public double BreakEvenDistance { get; set; }

        #endregion
        
        #region Indicators

        // Primary timeframe indicators
        private RelativeStrengthIndex _rsi;
        private MacdCrossOver _macd;
        private BollingerBands _bollingerBands;
        private AverageTrueRange _atr;
        private SimpleMovingAverage _sma20;
        private SimpleMovingAverage _sma50;
        private SimpleMovingAverage _sma200;

        // Higher timeframe indicators for trend confirmation
        private Bars _higherTimeframeBars;
        private RelativeStrengthIndex _rsiHTF;
        private MacdCrossOver _macdHTF;
        private SimpleMovingAverage _sma20HTF;
        private SimpleMovingAverage _sma50HTF;

        // Volume indicators
        private SimpleMovingAverage _volumeSMA;

        #endregion
        
        #region Private Variables

        private int _tradesCountToday;
        private int _consecutiveLosses;
        private DateTime _lastTradeDate;
        private double _dailyPnL;
        private bool _tradingEnabled;

        // Support and Resistance levels
        private double _currentSupport;
        private double _currentResistance;
        private double _previousHigh;
        private double _previousLow;
        private double[] _supportLevels;
        private double[] _resistanceLevels;

        // Market environment
        private bool _isTrendingMarket;
        private string _marketDirection;
        private string _higherTimeframeTrend;
        private double _currentVolatility;

        // Signal strength tracking
        private int _bullishSignalStrength;
        private int _bearishSignalStrength;
        private DateTime _lastSignalTime;

        // Status reporting
        private DateTime _lastStatusReport;
        private readonly TimeSpan _statusReportInterval = TimeSpan.FromHours(1);

        // Risk management
        private double _dailyStartBalance;
        private double _maxDailyDrawdownAmount;
        private bool _dailyDrawdownExceeded;

        // News events (simplified - in real implementation would use news feed)
        private readonly int[] _newsHours = { 8, 9, 13, 14, 15 }; // UTC hours with potential high impact news

        // Market session information
        private string _currentMarketSession;
        private double _sessionVolatilityMultiplier;
        private int _sessionMinSignalStrength;

        #endregion
        
        protected override void OnStart()
        {
            // Initialize primary timeframe indicators
            _rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, RSIPeriod);
            _macd = Indicators.MacdCrossOver(Bars.ClosePrices, MACDFastEMA, MACDSlowEMA, MACDSignal);
            _bollingerBands = Indicators.BollingerBands(Bars.ClosePrices, BBPeriod, BBStdDev, MovingAverageType.Simple);
            _atr = Indicators.AverageTrueRange(Bars, ATRPeriod, MovingAverageType.Simple);
            _sma20 = Indicators.SimpleMovingAverage(Bars.ClosePrices, 20);
            _sma50 = Indicators.SimpleMovingAverage(Bars.ClosePrices, 50);
            _sma200 = Indicators.SimpleMovingAverage(Bars.ClosePrices, 200);

            // Initialize higher timeframe indicators (4H for trend confirmation)
            // Note: In backtesting, we'll use current timeframe data for simplicity
            try
            {
                var higherTimeframe = TimeFrame.Hour4;
                _higherTimeframeBars = MarketData.GetBars(higherTimeframe, SymbolName);
                _rsiHTF = Indicators.RelativeStrengthIndex(_higherTimeframeBars.ClosePrices, RSIPeriod);
                _macdHTF = Indicators.MacdCrossOver(_higherTimeframeBars.ClosePrices, MACDFastEMA, MACDSlowEMA, MACDSignal);
                _sma20HTF = Indicators.SimpleMovingAverage(_higherTimeframeBars.ClosePrices, 20);
                _sma50HTF = Indicators.SimpleMovingAverage(_higherTimeframeBars.ClosePrices, 50);
            }
            catch
            {
                // Fallback to current timeframe if higher timeframe not available in backtest
                _higherTimeframeBars = Bars;
                _rsiHTF = _rsi;
                _macdHTF = _macd;
                _sma20HTF = _sma20;
                _sma50HTF = _sma50;
                Print("Using current timeframe for higher timeframe analysis (backtest mode)");
            }

            // Initialize volume indicator (use close prices as proxy if tick volumes not available)
            try
            {
                _volumeSMA = Indicators.SimpleMovingAverage(Bars.TickVolumes, 20);
            }
            catch
            {
                // Fallback: use price range as volume proxy for backtesting
                var priceRange = Indicators.SimpleMovingAverage(
                    Indicators.AverageTrueRange(Bars, 20, MovingAverageType.Simple).Result, 20);
                Print("Using price range as volume proxy (backtest mode)");
            }

            // Initialize variables
            _tradesCountToday = 0;
            _consecutiveLosses = 0;
            _lastTradeDate = DateTime.MinValue;
            _dailyPnL = 0;
            _tradingEnabled = true;
            _supportLevels = new double[5];
            _resistanceLevels = new double[5];
            _lastSignalTime = DateTime.MinValue;
            _dailyStartBalance = Account.Balance;
            _maxDailyDrawdownAmount = _dailyStartBalance * (MaxDailyDrawdown / 100);
            _dailyDrawdownExceeded = false;
            _lastStatusReport = Server.Time;

            Print("XAUUSD High Risk-Reward Bot Started");
            Print($"Risk per trade: {RiskPerTrade}%");
            Print($"Min Risk-Reward Ratio: {MinRiskRewardRatio}:1");
            Print($"Trading Hours: {TradingStartHour}:00 - {TradingEndHour}:00 UTC");
            Print($"Primary Timeframe: {TimeFrame}");
            Print($"Higher Timeframe: {TimeFrame.Hour4}");
        }
        
        protected override void OnTick()
        {
            // Check if new bar has formed
            if (Bars.Count < 100) // Need enough data for indicators
                return;

            // Reset daily counters if new day
            CheckNewTradingDay();

            // Check daily drawdown limit
            CheckDailyDrawdown();

            // Update market analysis
            UpdateMarketAnalysis();

            // Update market session analysis
            UpdateMarketSession();

            // Manage existing positions
            ManageExistingPositions();

            // Check trading conditions
            if (!IsTradingAllowed())
                return;

            // Look for trading signals
            CheckForTradingSignals();

            // Periodic status reporting
            if (Server.Time - _lastStatusReport >= _statusReportInterval)
            {
                PrintDailyStatistics();
                _lastStatusReport = Server.Time;
            }
        }
        
        protected override void OnPositionOpened(Position position)
        {
            if (position.Label.Contains("XAUUSD"))
            {
                Print($"Position opened: {position.TradeType} {position.VolumeInUnits} units at {position.EntryPrice}");
                Print($"Stop Loss: {position.StopLoss}, Take Profit: {position.TakeProfit}");
            }
        }

        protected override void OnPositionClosed(Position position)
        {
            var pnl = position.NetProfit;
            
            Print($"Position closed: {position.TradeType} PnL: {pnl:F2} {Account.Asset.Name}");
            
            // Update statistics
            _dailyPnL += pnl;
            
            if (pnl < 0)
            {
                _consecutiveLosses++;
                Print($"Consecutive losses: {_consecutiveLosses}");
            }
            else
            {
                _consecutiveLosses = 0;
            }
            
            // Check if trading should be disabled
            if (_consecutiveLosses >= MaxConsecutiveLosses)
            {
                _tradingEnabled = false;
                Print($"Trading disabled due to {MaxConsecutiveLosses} consecutive losses");
            }
        }
        
        #region Private Methods
        
        private void CheckNewTradingDay()
        {
            var currentDate = Server.Time.Date;

            if (_lastTradeDate != currentDate)
            {
                // Reset daily counters
                _tradesCountToday = 0;
                _dailyPnL = 0;
                _tradingEnabled = true;
                _consecutiveLosses = 0;
                _lastTradeDate = currentDate;
                _dailyStartBalance = Account.Balance;
                _maxDailyDrawdownAmount = _dailyStartBalance * (MaxDailyDrawdown / 100);
                _dailyDrawdownExceeded = false;

                Print($"New trading day: {currentDate:yyyy-MM-dd}");
                Print($"Daily start balance: {_dailyStartBalance:F2}");
                Print($"Max daily drawdown allowed: {_maxDailyDrawdownAmount:F2}");
            }
        }

        private void CheckDailyDrawdown()
        {
            var currentBalance = Account.Balance;
            var currentDrawdown = _dailyStartBalance - currentBalance;

            if (currentDrawdown > _maxDailyDrawdownAmount && !_dailyDrawdownExceeded)
            {
                _dailyDrawdownExceeded = true;
                _tradingEnabled = false;

                // Close all positions if drawdown exceeded
                foreach (var position in Positions)
                {
                    ClosePosition(position);
                }

                Print($"DAILY DRAWDOWN LIMIT EXCEEDED: {currentDrawdown:F2} > {_maxDailyDrawdownAmount:F2}");
                Print("All positions closed and trading disabled for today");
            }
        }

        private void ManageExistingPositions()
        {
            foreach (var position in Positions)
            {
                if (UseTrailingStop)
                {
                    UpdateTrailingStop(position);
                }

                // Move to break-even when profitable
                MoveToBreakEven(position);
            }
        }

        private void UpdateTrailingStop(Position position)
        {
            var atrValue = _atr.Result.LastValue;
            var trailingDistance = atrValue * TrailingStopDistance;

            if (position.TradeType == TradeType.Buy)
            {
                var newStopLoss = Symbol.Bid - trailingDistance;
                if (position.StopLoss == null || newStopLoss > position.StopLoss)
                {
                    var result = ModifyPosition(position, newStopLoss, position.TakeProfit);
                    if (!result.IsSuccessful)
                        Print($"Failed to modify position: {result.Error}");
                }
            }
            else if (position.TradeType == TradeType.Sell)
            {
                var newStopLoss = Symbol.Ask + trailingDistance;
                if (position.StopLoss == null || newStopLoss < position.StopLoss)
                {
                    var result = ModifyPosition(position, newStopLoss, position.TakeProfit);
                    if (!result.IsSuccessful)
                        Print($"Failed to modify position: {result.Error}");
                }
            }
        }

        private void MoveToBreakEven(Position position)
        {
            var atrValue = _atr.Result.LastValue;
            var breakEvenDistance = atrValue * BreakEvenDistance;

            if (position.TradeType == TradeType.Buy)
            {
                var profitDistance = Symbol.Bid - position.EntryPrice;
                if (profitDistance >= breakEvenDistance && (position.StopLoss == null || position.StopLoss < position.EntryPrice))
                {
                    var result = ModifyPosition(position, position.EntryPrice + Symbol.PipSize, position.TakeProfit);
                    if (result.IsSuccessful)
                        Print($"Position moved to break-even: {position.Id}");
                    else
                        Print($"Failed to move to break-even: {result.Error}");
                    Print($"Position moved to break-even: {position.Id}");
                }
            }
            else if (position.TradeType == TradeType.Sell)
            {
                var profitDistance = position.EntryPrice - Symbol.Ask;
                if (profitDistance >= breakEvenDistance && (position.StopLoss == null || position.StopLoss > position.EntryPrice))
                {
                    var result = ModifyPosition(position, position.EntryPrice - Symbol.PipSize, position.TakeProfit);
                    if (result.IsSuccessful)
                        Print($"Position moved to break-even: {position.Id}");
                    else
                        Print($"Failed to move to break-even: {result.Error}");
                }
            }
        }
        
        private void UpdateMarketAnalysis()
        {
            // Calculate support and resistance levels
            CalculateSupportResistance();

            // Determine market environment
            DetermineMarketEnvironment();
        }

        private void UpdateMarketSession()
        {
            var currentHour = Server.Time.Hour;

            // Determine current market session
            if (currentHour >= 0 && currentHour < 8)
            {
                _currentMarketSession = "ASIAN";
                _sessionVolatilityMultiplier = 0.7; // Lower volatility expected
                _sessionMinSignalStrength = 8; // Higher threshold for Asian session
            }
            else if (currentHour >= 8 && currentHour < 13)
            {
                _currentMarketSession = "LONDON";
                _sessionVolatilityMultiplier = 1.0; // Normal volatility
                _sessionMinSignalStrength = 7; // Standard threshold
            }
            else if (currentHour >= 13 && currentHour < 17)
            {
                _currentMarketSession = "LONDON_NY_OVERLAP";
                _sessionVolatilityMultiplier = 1.3; // Higher volatility expected
                _sessionMinSignalStrength = 6; // Lower threshold for best session
            }
            else if (currentHour >= 17 && currentHour < 22)
            {
                _currentMarketSession = "NEW_YORK";
                _sessionVolatilityMultiplier = 1.1; // Slightly higher volatility
                _sessionMinSignalStrength = 7; // Standard threshold
            }
            else
            {
                _currentMarketSession = "AFTER_HOURS";
                _sessionVolatilityMultiplier = 0.6; // Very low volatility
                _sessionMinSignalStrength = 9; // Very high threshold
            }
        }
        
        private void CalculateSupportResistance()
        {
            // Simplified support/resistance calculation
            var lookbackPeriod = 20;

            if (Bars.Count < lookbackPeriod)
                return;

            // Calculate recent highs and lows
            double maxHigh = double.MinValue;
            double minLow = double.MaxValue;

            for (int i = 0; i < lookbackPeriod; i++)
            {
                var index = Bars.Count - 1 - i;
                if (index >= 0)
                {
                    maxHigh = Math.Max(maxHigh, Bars.HighPrices[index]);
                    minLow = Math.Min(minLow, Bars.LowPrices[index]);
                }
            }

            _currentResistance = maxHigh;
            _currentSupport = minLow;

            // Initialize arrays if needed
            if (_resistanceLevels == null)
                _resistanceLevels = new double[5];
            if (_supportLevels == null)
                _supportLevels = new double[5];

            // Store current levels
            _resistanceLevels[0] = _currentResistance;
            _supportLevels[0] = _currentSupport;

            _previousHigh = Bars.HighPrices[Bars.Count - 2];
            _previousLow = Bars.LowPrices[Bars.Count - 2];
        }
        
        private void DetermineMarketEnvironment()
        {
            // Enhanced market environment analysis
            var sma20Value = _sma20.Result.LastValue;
            var sma50Value = _sma50.Result.LastValue;
            var sma200Value = _sma200.Result.LastValue;
            var currentPrice = Bars.ClosePrices.LastValue;
            var atrValue = _atr.Result.LastValue;

            // Calculate volatility
            _currentVolatility = atrValue / currentPrice * 100; // ATR as percentage of price

            // Determine trending vs ranging market
            var smaSpread = Math.Abs(sma20Value - sma50Value) / currentPrice * 100;
            _isTrendingMarket = smaSpread > 0.1; // 0.1% spread threshold

            // Primary timeframe trend
            if (sma20Value > sma50Value && currentPrice > sma20Value && sma50Value > sma200Value)
                _marketDirection = "STRONG_BULLISH";
            else if (sma20Value > sma50Value && currentPrice > sma20Value)
                _marketDirection = "BULLISH";
            else if (sma20Value < sma50Value && currentPrice < sma20Value && sma50Value < sma200Value)
                _marketDirection = "STRONG_BEARISH";
            else if (sma20Value < sma50Value && currentPrice < sma20Value)
                _marketDirection = "BEARISH";
            else
                _marketDirection = "NEUTRAL";

            // Higher timeframe trend confirmation
            if (_higherTimeframeBars.Count > 50)
            {
                var htfSma20 = _sma20HTF.Result.LastValue;
                var htfSma50 = _sma50HTF.Result.LastValue;
                var htfPrice = _higherTimeframeBars.ClosePrices.LastValue;

                if (htfSma20 > htfSma50 && htfPrice > htfSma20)
                    _higherTimeframeTrend = "BULLISH";
                else if (htfSma20 < htfSma50 && htfPrice < htfSma20)
                    _higherTimeframeTrend = "BEARISH";
                else
                    _higherTimeframeTrend = "NEUTRAL";
            }
        }
        
        private bool IsTradingAllowed()
        {
            // Check if trading is enabled
            if (!_tradingEnabled)
                return false;

            // Check daily drawdown limit
            if (_dailyDrawdownExceeded)
                return false;

            // Check daily trade limit
            if (_tradesCountToday >= MaxTradesPerDay)
                return false;

            // Check trading hours
            var currentHour = Server.Time.Hour;
            if (currentHour < TradingStartHour || currentHour >= TradingEndHour)
                return false;

            // Check session-specific filters
            if (AvoidAsianSession && _currentMarketSession == "ASIAN")
            {
                Print("Trading avoided during Asian session");
                return false;
            }

            if (AvoidAsianSession && _currentMarketSession == "AFTER_HOURS")
            {
                Print("Trading avoided during after-hours session");
                return false;
            }

            // Prefer London-NY overlap if enabled
            if (PreferLondonNYOverlap && _currentMarketSession != "LONDON_NY_OVERLAP" &&
                _currentMarketSession != "LONDON" && _currentMarketSession != "NEW_YORK")
            {
                Print($"Trading avoided outside preferred sessions. Current: {_currentMarketSession}");
                return false;
            }

            // Check if there are existing positions
            if (Positions.Count > 0)
                return false;

            // Check news filter
            if (UseNewsFilter && IsHighImpactNewsTime())
            {
                Print("Trading paused due to high-impact news time");
                return false;
            }

            // Check market volatility (avoid extremely volatile conditions)
            if (_currentVolatility > 3.0)
            {
                Print($"Trading paused due to extreme volatility: {_currentVolatility:F2}%");
                return false;
            }

            return true;
        }
        
        private bool IsHighImpactNewsTime()
        {
            var currentHour = Server.Time.Hour;
            var currentMinute = Server.Time.Minute;

            // Check if current time is within 30 minutes of major news hours
            foreach (var newsHour in _newsHours)
            {
                if (Math.Abs(currentHour - newsHour) == 0 && currentMinute <= 30)
                {
                    return true;
                }
                if (currentHour == newsHour - 1 && currentMinute >= 30)
                {
                    return true;
                }
            }

            // Additional checks for specific high-impact times
            // Friday 13:30 UTC (NFP)
            if (Server.Time.DayOfWeek == DayOfWeek.Friday && currentHour == 13 && currentMinute >= 15 && currentMinute <= 45)
            {
                return true;
            }

            // FOMC meeting times (typically 19:00 UTC on meeting days)
            // This is a simplified check - in practice, you'd use a news calendar API
            if (currentHour == 19 && currentMinute <= 30)
            {
                return true;
            }

            return false;
        }
        
        private void CheckForTradingSignals()
        {
            // Check for bullish signals
            if (IsBullishSignal())
            {
                ExecuteBuyOrder();
            }
            // Check for bearish signals
            else if (IsBearishSignal())
            {
                ExecuteSellOrder();
            }
        }
        
        private bool IsBullishSignal()
        {
            _bullishSignalStrength = CalculateBullishSignalStrength();

            // Use session-specific minimum signal strength
            var minSignalStrength = _sessionMinSignalStrength;

            // Additional filters
            bool signalTimingOk = (Server.Time - _lastSignalTime).TotalMinutes > 30; // Avoid rapid signals
            bool volatilityOk = _currentVolatility > 0.05 && _currentVolatility < 2.0; // Reasonable volatility
            bool sessionOk = _currentMarketSession != "AFTER_HOURS"; // Avoid after-hours trading

            Print($"Bullish signal strength: {_bullishSignalStrength}/{minSignalStrength} (Session: {_currentMarketSession})");

            return _bullishSignalStrength >= minSignalStrength && signalTimingOk && volatilityOk && sessionOk;
        }

        private int CalculateBullishSignalStrength()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var rsiValue = _rsi.Result.LastValue;
            var macdMain = _macd.MACD.LastValue;
            var macdSignal = _macd.Signal.LastValue;
            var bbUpper = _bollingerBands.Top.LastValue;
            var bbMiddle = _bollingerBands.Main.LastValue;
            // Volume analysis (use price range as proxy if tick volumes not available)
            var volume = GetVolumeProxy();
            var avgVolume = GetAverageVolumeProxy();

            int signalStrength = 0;

            // 1. Price breakout confirmation (2 points)
            if (currentPrice > _currentResistance)
                signalStrength += 2;
            else if (currentPrice > bbUpper)
                signalStrength += 1;

            // 2. RSI confirmation (1 point)
            if (rsiValue > 50 && rsiValue < 75) // Sweet spot
                signalStrength += 1;

            // 3. MACD confirmation (2 points)
            if (macdMain > macdSignal && macdMain > 0)
                signalStrength += 2;
            else if (macdMain > macdSignal)
                signalStrength += 1;

            // 4. Trend alignment (2 points)
            if (_marketDirection == "STRONG_BULLISH" && _higherTimeframeTrend == "BULLISH")
                signalStrength += 2;
            else if (_marketDirection == "BULLISH" || _higherTimeframeTrend == "BULLISH")
                signalStrength += 1;

            // 5. Volume confirmation (1 point)
            if (volume > avgVolume * 1.2)
                signalStrength += 1;

            // 6. Price position relative to moving averages (1 point)
            if (currentPrice > _sma20.Result.LastValue && _sma20.Result.LastValue > _sma50.Result.LastValue)
                signalStrength += 1;

            // 7. Higher timeframe RSI confirmation (1 point)
            if (_higherTimeframeBars.Count > RSIPeriod && _rsiHTF.Result.LastValue > 50)
                signalStrength += 1;

            return Math.Min(signalStrength, 10); // Cap at 10
        }
        
        private bool IsBearishSignal()
        {
            _bearishSignalStrength = CalculateBearishSignalStrength();

            // Use session-specific minimum signal strength
            var minSignalStrength = _sessionMinSignalStrength;

            // Additional filters
            bool signalTimingOk = (Server.Time - _lastSignalTime).TotalMinutes > 30; // Avoid rapid signals
            bool volatilityOk = _currentVolatility > 0.05 && _currentVolatility < 2.0; // Reasonable volatility
            bool sessionOk = _currentMarketSession != "AFTER_HOURS"; // Avoid after-hours trading

            Print($"Bearish signal strength: {_bearishSignalStrength}/{minSignalStrength} (Session: {_currentMarketSession})");

            return _bearishSignalStrength >= minSignalStrength && signalTimingOk && volatilityOk && sessionOk;
        }

        private int CalculateBearishSignalStrength()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var rsiValue = _rsi.Result.LastValue;
            var macdMain = _macd.MACD.LastValue;
            var macdSignal = _macd.Signal.LastValue;
            var bbLower = _bollingerBands.Bottom.LastValue;
            var bbMiddle = _bollingerBands.Main.LastValue;
            // Volume analysis (use price range as proxy if tick volumes not available)
            var volume = GetVolumeProxy();
            var avgVolume = GetAverageVolumeProxy();

            int signalStrength = 0;

            // 1. Price breakout confirmation (2 points)
            if (currentPrice < _currentSupport)
                signalStrength += 2;
            else if (currentPrice < bbLower)
                signalStrength += 1;

            // 2. RSI confirmation (1 point)
            if (rsiValue < 50 && rsiValue > 25) // Sweet spot
                signalStrength += 1;

            // 3. MACD confirmation (2 points)
            if (macdMain < macdSignal && macdMain < 0)
                signalStrength += 2;
            else if (macdMain < macdSignal)
                signalStrength += 1;

            // 4. Trend alignment (2 points)
            if (_marketDirection == "STRONG_BEARISH" && _higherTimeframeTrend == "BEARISH")
                signalStrength += 2;
            else if (_marketDirection == "BEARISH" || _higherTimeframeTrend == "BEARISH")
                signalStrength += 1;

            // 5. Volume confirmation (1 point)
            if (volume > avgVolume * 1.2)
                signalStrength += 1;

            // 6. Price position relative to moving averages (1 point)
            if (currentPrice < _sma20.Result.LastValue && _sma20.Result.LastValue < _sma50.Result.LastValue)
                signalStrength += 1;

            // 7. Higher timeframe RSI confirmation (1 point)
            if (_higherTimeframeBars.Count > RSIPeriod && _rsiHTF.Result.LastValue < 50)
                signalStrength += 1;

            return Math.Min(signalStrength, 10); // Cap at 10
        }
        

        private void ExecuteBuyOrder()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var atrValue = _atr.Result.LastValue;

            // Calculate position size based on risk management
            var stopLossDistance = atrValue * ATRStopLossMultiplier;
            var takeProfitDistance = atrValue * ATRTakeProfitMultiplier;

            // Ensure minimum risk-reward ratio
            var actualRiskReward = takeProfitDistance / stopLossDistance;
            if (actualRiskReward < MinRiskRewardRatio)
            {
                takeProfitDistance = stopLossDistance * MinRiskRewardRatio;
                Print($"Adjusted take profit to maintain {MinRiskRewardRatio}:1 risk-reward ratio");
            }

            var stopLossPrice = currentPrice - stopLossDistance;
            var takeProfitPrice = currentPrice + takeProfitDistance;

            // Calculate position size based on risk percentage
            var riskAmount = Account.Balance * (RiskPerTrade / 100);
            var positionSize = CalculatePositionSize(riskAmount, stopLossDistance);

            if (positionSize > 0)
            {
                var result = ExecuteMarketOrder(TradeType.Buy, SymbolName, positionSize, "XAUUSD_HighRR_Buy",
                    stopLossPrice, takeProfitPrice);

                if (result.IsSuccessful)
                {
                    _tradesCountToday++;
                    _lastSignalTime = Server.Time;
                    Print($"=== BUY ORDER EXECUTED ===");
                    Print($"Time: {Server.Time:yyyy-MM-dd HH:mm:ss} UTC");
                    Print($"Session: {_currentMarketSession}");
                    Print($"Entry Price: {currentPrice:F2}");
                    Print($"Position Size: {positionSize} units");
                    Print($"Stop Loss: {stopLossPrice:F2} (Distance: {stopLossDistance:F2})");
                    Print($"Take Profit: {takeProfitPrice:F2} (Distance: {takeProfitDistance:F2})");
                    Print($"Risk-Reward Ratio: {actualRiskReward:F2}:1");
                    Print($"Signal Strength: {_bullishSignalStrength}/10");
                    Print($"Risk Amount: {riskAmount:F2} {Account.Asset.Name}");
                    PrintTradeAnalysis();
                    Print($"========================");
                }
                else
                {
                    Print($"Buy order failed: {result.Error}");
                }
            }
        }

        private void ExecuteSellOrder()
        {
            var currentPrice = Bars.ClosePrices.LastValue;
            var atrValue = _atr.Result.LastValue;

            // Calculate position size based on risk management
            var stopLossDistance = atrValue * ATRStopLossMultiplier;
            var takeProfitDistance = atrValue * ATRTakeProfitMultiplier;

            // Ensure minimum risk-reward ratio
            var actualRiskReward = takeProfitDistance / stopLossDistance;
            if (actualRiskReward < MinRiskRewardRatio)
            {
                takeProfitDistance = stopLossDistance * MinRiskRewardRatio;
                Print($"Adjusted take profit to maintain {MinRiskRewardRatio}:1 risk-reward ratio");
            }

            var stopLossPrice = currentPrice + stopLossDistance;
            var takeProfitPrice = currentPrice - takeProfitDistance;

            // Calculate position size based on risk percentage
            var riskAmount = Account.Balance * (RiskPerTrade / 100);
            var positionSize = CalculatePositionSize(riskAmount, stopLossDistance);

            if (positionSize > 0)
            {
                var result = ExecuteMarketOrder(TradeType.Sell, SymbolName, positionSize, "XAUUSD_HighRR_Sell",
                    stopLossPrice, takeProfitPrice);

                if (result.IsSuccessful)
                {
                    _tradesCountToday++;
                    _lastSignalTime = Server.Time;
                    Print($"=== SELL ORDER EXECUTED ===");
                    Print($"Time: {Server.Time:yyyy-MM-dd HH:mm:ss} UTC");
                    Print($"Session: {_currentMarketSession}");
                    Print($"Entry Price: {currentPrice:F2}");
                    Print($"Position Size: {positionSize} units");
                    Print($"Stop Loss: {stopLossPrice:F2} (Distance: {stopLossDistance:F2})");
                    Print($"Take Profit: {takeProfitPrice:F2} (Distance: {takeProfitDistance:F2})");
                    Print($"Risk-Reward Ratio: {actualRiskReward:F2}:1");
                    Print($"Signal Strength: {_bearishSignalStrength}/10");
                    Print($"Risk Amount: {riskAmount:F2} {Account.Asset.Name}");
                    PrintTradeAnalysis();
                    Print($"=========================");
                }
                else
                {
                    Print($"Sell order failed: {result.Error}");
                }
            }
        }

        private long CalculatePositionSize(double riskAmount, double stopLossDistance)
        {
            // Calculate position size based on risk amount and stop loss distance
            var pipValue = Symbol.PipValue;
            var stopLossInPips = stopLossDistance / Symbol.PipSize;

            // Position size = Risk Amount / (Stop Loss in Pips * Pip Value)
            var positionSize = riskAmount / (stopLossInPips * pipValue);

            // Convert to volume in units and normalize
            var volumeInUnits = Symbol.NormalizeVolumeInUnits(positionSize, RoundingMode.Down);

            // Ensure minimum volume
            if (volumeInUnits < Symbol.VolumeInUnitsMin)
                volumeInUnits = Symbol.VolumeInUnitsMin;

            // Ensure maximum volume (safety check)
            var maxVolume = Account.Balance * 0.1; // Max 10% of balance as position value
            var maxVolumeInUnits = Symbol.NormalizeVolumeInUnits(maxVolume / Symbol.Bid, RoundingMode.Down);
            if (volumeInUnits > maxVolumeInUnits)
                volumeInUnits = maxVolumeInUnits;

            return volumeInUnits;
        }


        private double GetVolumeProxy()
        {
            try
            {
                return Bars.TickVolumes.LastValue;
            }
            catch
            {
                // Use price range as volume proxy for backtesting
                var high = Bars.HighPrices.LastValue;
                var low = Bars.LowPrices.LastValue;
                return (high - low) / Symbol.PipSize; // Normalized price range
            }
        }

        private double GetAverageVolumeProxy()
        {
            try
            {
                return _volumeSMA.Result.LastValue;
            }
            catch
            {
                // Use average ATR as volume proxy
                return _atr.Result.LastValue / Symbol.PipSize;
            }
        }

        private void PrintDailyStatistics()
        {
            var winRate = _tradesCountToday > 0 ? (double)(_tradesCountToday - _consecutiveLosses) / _tradesCountToday * 100 : 0;
            var currentBalance = Account.Balance;
            var dailyReturn = _dailyStartBalance > 0 ? (currentBalance - _dailyStartBalance) / _dailyStartBalance * 100 : 0;

            Print("=== Daily Statistics ===");
            Print($"Date: {Server.Time:yyyy-MM-dd HH:mm:ss} UTC");
            Print($"Market Session: {_currentMarketSession}");
            Print($"Trades today: {_tradesCountToday}/{MaxTradesPerDay}");
            Print($"Daily PnL: {_dailyPnL:F2} {Account.Asset.Name}");
            Print($"Daily Return: {dailyReturn:F2}%");
            Print($"Win Rate: {winRate:F1}%");
            Print($"Consecutive losses: {_consecutiveLosses}/{MaxConsecutiveLosses}");
            Print($"Trading enabled: {_tradingEnabled}");
            Print($"Drawdown exceeded: {_dailyDrawdownExceeded}");
            Print($"Current volatility: {_currentVolatility:F2}%");
            Print($"Market direction: {_marketDirection}");
            Print($"Higher TF trend: {_higherTimeframeTrend}");
            Print($"Current support: {_currentSupport:F2}");
            Print($"Current resistance: {_currentResistance:F2}");
            Print($"Account Balance: {currentBalance:F2} {Account.Asset.Name}");
            Print($"Account Equity: {Account.Equity:F2} {Account.Asset.Name}");
            Print($"Free Margin: {Account.FreeMargin:F2} {Account.Asset.Name}");
            Print("========================");
        }

        private void PrintTradeAnalysis()
        {
            Print("=== Trade Analysis ===");
            Print($"RSI: {_rsi.Result.LastValue:F2}");
            Print($"MACD: {_macd.MACD.LastValue:F4} | Signal: {_macd.Signal.LastValue:F4}");
            Print($"BB Upper: {_bollingerBands.Top.LastValue:F2}");
            Print($"BB Lower: {_bollingerBands.Bottom.LastValue:F2}");
            Print($"ATR: {_atr.Result.LastValue:F2}");
            Print($"SMA20: {_sma20.Result.LastValue:F2}");
            Print($"SMA50: {_sma50.Result.LastValue:F2}");
            Print($"Current Price: {Bars.ClosePrices.LastValue:F2}");
            Print($"Volume: {GetVolumeProxy():F0} | Avg: {GetAverageVolumeProxy():F0}");

            if (_higherTimeframeBars.Count > RSIPeriod)
            {
                Print($"HTF RSI: {_rsiHTF.Result.LastValue:F2}");
                Print($"HTF MACD: {_macdHTF.MACD.LastValue:F4}");
            }

            Print("======================");
        }

        protected override void OnStop()
        {
            PrintDailyStatistics();
            Print("XAUUSD High Risk-Reward Bot Stopped");
        }

        #endregion
    }
}
