using System;
using cAlgo.API;
using cAlgo.API.Indicators;

namespace cAlgo.Robots
{
    [Robot(TimeZone = TimeZones.UTC, AccessRights = AccessRights.None)]
    public class XAUUSDHighRRBot_Test : Robot
    {
        #region Parameters
        [Parameter("Risk Per Trade (%)", DefaultValue = 1.5, MinValue = 0.1, MaxValue = 5.0)]
        public double RiskPerTrade { get; set; }

        [Parameter("Min Risk Reward Ratio", DefaultValue = 2.5, MinValue = 1.5, MaxValue = 5.0)]
        public double MinRiskRewardRatio { get; set; }

        [Parameter("ATR Period", DefaultValue = 14, MinValue = 5, MaxValue = 50)]
        public int ATRPeriod { get; set; }

        [Parameter("ATR Stop Loss Multiplier", DefaultValue = 1.5, MinValue = 0.5, MaxValue = 3.0)]
        public double ATRStopLossMultiplier { get; set; }

        [Parameter("ATR Take Profit Multiplier", DefaultValue = 3.75, MinValue = 1.0, MaxValue = 10.0)]
        public double ATRTakeProfitMultiplier { get; set; }
        #endregion

        #region Private Fields
        private AverageTrueRange _atr;
        private RelativeStrengthIndex _rsi;
        private MacdCrossOver _macd;
        private BollingerBands _bollingerBands;
        #endregion

        protected override void OnStart()
        {
            // Initialize indicators
            _atr = Indicators.AverageTrueRange(Bars, ATRPeriod, MovingAverageType.Simple);
            _rsi = Indicators.RelativeStrengthIndex(Bars.ClosePrices, 14);
            _macd = Indicators.MacdCrossOver(Bars.ClosePrices, 12, 26, 9);
            _bollingerBands = Indicators.BollingerBands(Bars.ClosePrices, 20, 2.0, MovingAverageType.Simple);
            
            Print("XAUUSD High RR Bot Test - Started Successfully");
            Print($"Risk Per Trade: {RiskPerTrade}%");
            Print($"Min Risk Reward Ratio: {MinRiskRewardRatio}:1");
            Print($"ATR Period: {ATRPeriod}");
        }

        protected override void OnTick()
        {
            // Basic validation
            if (_atr.Result.LastValue == 0 || double.IsNaN(_atr.Result.LastValue))
                return;

            // Test signal generation
            if (Bars.Count % 100 == 0) // Every 100 ticks
            {
                TestSignalGeneration();
            }
        }

        private void TestSignalGeneration()
        {
            try
            {
                var currentPrice = Symbol.Bid;
                var atrValue = _atr.Result.LastValue;
                var rsiValue = _rsi.Result.LastValue;
                var macdMain = _macd.MACD.LastValue;
                var macdSignal = _macd.Signal.LastValue;
                var bbUpper = _bollingerBands.Top.LastValue;
                var bbLower = _bollingerBands.Bottom.LastValue;

                Print($"Test - Price: {currentPrice:F2}, ATR: {atrValue:F2}, RSI: {rsiValue:F1}");
                Print($"MACD: {macdMain:F4}, Signal: {macdSignal:F4}");
                Print($"BB Upper: {bbUpper:F2}, Lower: {bbLower:F2}");

                // Test position sizing
                var stopLossDistance = atrValue * ATRStopLossMultiplier;
                var takeProfitDistance = atrValue * ATRTakeProfitMultiplier;
                var riskRewardRatio = takeProfitDistance / stopLossDistance;

                Print($"Stop Loss Distance: {stopLossDistance:F2}");
                Print($"Take Profit Distance: {takeProfitDistance:F2}");
                Print($"Risk Reward Ratio: {riskRewardRatio:F2}:1");

                if (riskRewardRatio >= MinRiskRewardRatio)
                {
                    var positionSize = CalculatePositionSize(Account.Balance * RiskPerTrade / 100, stopLossDistance);
                    Print($"Position Size: {positionSize} units");
                }
                else
                {
                    Print("Risk reward ratio too low - no trade");
                }
            }
            catch (Exception ex)
            {
                Print($"Error in TestSignalGeneration: {ex.Message}");
            }
        }

        private long CalculatePositionSize(double riskAmount, double stopLossDistance)
        {
            try
            {
                var pipValue = Symbol.PipValue;
                var stopLossInPips = stopLossDistance / Symbol.PipSize;
                var positionSize = riskAmount / (stopLossInPips * pipValue);
                var volumeInUnits = Symbol.NormalizeVolumeInUnits(positionSize, RoundingMode.Down);

                if (volumeInUnits < Symbol.VolumeInUnitsMin)
                    volumeInUnits = Symbol.VolumeInUnitsMin;

                return volumeInUnits;
            }
            catch (Exception ex)
            {
                Print($"Error calculating position size: {ex.Message}");
                return Symbol.VolumeInUnitsMin;
            }
        }

        protected override void OnStop()
        {
            Print("XAUUSD High RR Bot Test - Stopped");
        }
    }
}
